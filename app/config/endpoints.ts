import { uniqueBy } from '@kdt310722/utils/array'
import { isString } from '@kdt310722/utils/string'
import { z } from 'zod'
import { heartbeat } from '../utils/schemas/heartbeat'
import { httpHeaders, httpMethod, httpUrl, wsUrl } from '../utils/schemas/requests'
import { retry } from '../utils/schemas/retry'

const timeout = z.number().positive()

const pool = z.object({
    connections: z.number().int().positive().nullish().default(null),
    clientTtl: timeout.nullish().default(null),
    pipelining: z.number().int().nonnegative().nullish().default(1),
    keepAliveTimeout: timeout.nullish().default(60_000),
    keepAliveMaxTimeout: timeout.nullish().default(600_000),
    keepAliveTimeoutThreshold: timeout.nullish().default(3000),
    autoSelectFamily: z.boolean().nullish(),
    autoSelectFamilyAttemptTimeout: timeout.nullish(),
    allowH2: z.boolean().nullish().default(false),
    maxConcurrentStreams: z.number().int().positive().nullish(),
})

const dns = z.object({
    enabled: z.boolean().default(true),
    maxTTL: timeout.nullish().default(60 * 60 * 60 * 1000),
    maxItems: z.number().int().positive().nullish().default(Number.POSITIVE_INFINITY),
})

const httpHeartbeat = z.object({
    enabled: z.boolean().default(true),
    interval: z.number().positive().default(30_000),
    method: httpMethod.default('OPTIONS'),
    headers: httpHeaders.default({}),
    body: z.string().nullish(),
})

const httpSchema = z.object({
    url: httpUrl,
    maxRequestsPerSecond: z.number().int().positive().nullish(),
    headers: httpHeaders.default({}),
    heartbeat: httpHeartbeat.default({}),
    timeout: timeout.default(10_000),
    retry: retry.default({}),
    pool: pool.default({}),
    dns: dns.default({}),
})

const http = z.union([httpSchema, httpUrl]).transform((val) => {
    return isString(val) ? httpSchema.parse({ url: val }) : val
})

const websocketTimeout = z.object({
    connect: timeout.default(10_000),
    disconnect: timeout.default(10_000),
    request: timeout.default(10_000),
})

const websocketSchema = z.object({
    url: wsUrl,
    reconnect: retry.default({}),
    timeout: websocketTimeout.default({}),
    heartbeat: heartbeat.default({}),
})

const websocket = z.union([websocketSchema, wsUrl]).transform((val) => (isString(val) ? websocketSchema.parse({ url: val }) : val)).nullish()

const endpoint = z.object({
    name: z.string().nonempty(),
    enabled: z.boolean().default(true),
    http,
    websocket,
})

export type EndpointConfig = z.infer<typeof endpoint>

export const endpoints = z.array(endpoint).nonempty().refine((val) => uniqueBy(val, (a, b) => a.name === b.name).length === val.length).transform((endpoints) => endpoints.filter((i) => i.enabled))
