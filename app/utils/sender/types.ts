import type { Dispatcher } from 'undici'
import type { HttpMethod } from './constants'

export type HttpMethodString = `${HttpMethod}` | Lowercase<`${HttpMethod}`>

export type HttpMethodType = HttpMethod | HttpMethodString

export interface SenderRequest {
    id: string
    method: Dispatcher.HttpMethod
    headers: Record<string, string>
    body?: string
    metadata: Record<string, unknown>
}

export interface SenderResponse<TBody = string> {
    id: string
    status: number
    headers: Record<string, string>
    body: TBody
    took: bigint
    metadata: Record<string, unknown>
}
