import { isNullish } from '@kdt310722/utils/common'
import { minMax, random } from '@kdt310722/utils/number'
import { type Awaitable, sleep } from '@kdt310722/utils/promise'

export interface GetRetryDelayOptions {
    delay?: number
    backoff?: number
    jitter?: number
    maxDelay?: number
}

export interface SenderRetryOptions<T> extends GetRetryDelayOptions {
    enabled?: boolean
    retries?: number
    shouldRetry?: (error: unknown) => Awaitable<boolean>
    shouldRetryOnResponse?: (response: T) => Awaitable<boolean>
    onFailedAttempt?: (error: unknown, attempts: number, retriesLeft: number) => Awaitable<void>
    onResponseAttempt?: (response: T, attempts: number, retriesLeft: number) => Awaitable<void>
}

export function getRetryDelay(attempts: number, { delay = 1000, backoff = 2, jitter = 0.1, maxDelay = 10_000 }: GetRetryDelayOptions = {}) {
    const exponentialDelay = delay * backoff ** attempts
    const jitterAmount = exponentialDelay * jitter
    const jitterRange = random(-jitterAmount, jitterAmount)
    const finalDelay = exponentialDelay + jitterRange

    return minMax(finalDelay, 0, maxDelay)
}

export async function withRetry<T>(fn: () => Awaitable<T>, { enabled = true, retries = 3, shouldRetry, shouldRetryOnResponse, onFailedAttempt, onResponseAttempt, ...delayOptions }: SenderRetryOptions<T> = {}): Promise<T> {
    if (!enabled) {
        return await fn()
    }

    const errors: unknown[] = []

    let attempt = 0
    let result: T

    while (attempt <= retries) {
        try {
            result = await fn()
        } catch (error) {
            errors.push(error)

            if (!isNullish(onFailedAttempt)) {
                await onFailedAttempt(error, attempt + 1, retries - attempt)
            }

            if (!isNullish(shouldRetry)) {
                const shouldRetryResult = await shouldRetry(error)

                if (!shouldRetryResult) {
                    throw errors.length === 1 ? error : new AggregateError(errors, `Failed after ${attempt + 1} attempts`)
                }
            }

            if (attempt < retries) {
                await sleep(getRetryDelay(attempt, delayOptions))
                attempt++
                continue
            }

            throw new AggregateError(errors, `Failed after ${attempt + 1} attempts`)
        }

        if (!isNullish(shouldRetryOnResponse)) {
            const shouldRetryResult = await shouldRetryOnResponse(result)

            if (shouldRetryResult) {
                if (!isNullish(onResponseAttempt)) {
                    await onResponseAttempt(result, attempt + 1, retries - attempt)
                }

                if (attempt < retries) {
                    await sleep(getRetryDelay(attempt, delayOptions))
                    attempt++
                    continue
                }
            }
        }

        return result
    }

    throw new AggregateError(errors, `Failed after ${attempt} attempts`)
}
