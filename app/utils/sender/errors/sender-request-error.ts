import type { Dispatcher } from 'undici'
import type { SenderRequest, SenderResponse } from '../types'
import { BaseError } from '@kdt310722/utils/error'

export class SenderRequestError extends BaseError {
    public declare readonly request: SenderRequest
    public declare readonly undiciResponse: Dispatcher.ResponseData
    public declare readonly response: SenderResponse<unknown>

    public withRequest(request?: SenderRequest) {
        return this.withValue('request', request)
    }

    public withUndiciResponse(response?: Dispatcher.ResponseData) {
        return this.withValue('undiciResponse', response)
    }

    public withResponse(response?: SenderResponse<unknown>) {
        return this.withValue('response', response)
    }
}
